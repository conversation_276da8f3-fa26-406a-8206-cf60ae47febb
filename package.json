{"name": "checkweather-sg", "version": "4.0.0", "description": "Yet another weather app for Singapore", "scripts": {"start": "vite", "build": "vite build", "serve": "vite preview"}, "keywords": ["weather", "rain", "rainfall", "singapore", "map"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@turf/helpers": "~7.2.0", "d3-contour": "~4.0.2", "firebase": "~11.9.1", "maplibre-gl": "~5.6.0", "nano-memoize": "~3.0.16", "preact": "~10.26.9", "react-use": "~17.6.0"}, "devDependencies": {"@preact/preset-vite": "~2.10.1", "@vitejs/plugin-legacy": "~6.1.1", "autoprefixer": "~10.4.21", "postcss-import-url": "~7.2.0", "vite": "~6.3.5", "vite-plugin-pwa": "~1.0.0", "workbox-cacheable-response": "~7.3.0", "workbox-expiration": "~7.3.0", "workbox-precaching": "~7.3.0", "workbox-routing": "~7.3.0", "workbox-strategies": "~7.3.0"}, "browserslist": [">0.2%", "not dead", "not op_mini all", "not chrome < 51", "not safari < 10"]}