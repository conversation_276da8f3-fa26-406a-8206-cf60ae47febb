<svg xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="1.4" clip-rule="evenodd" viewBox="0 0 512 512">
  <path fill="url(#_Linear1)" d="M0 0h512v512H0z"/>
  <path fill="#1d1b13" fill-rule="nonzero" d="M216 85.8a132 132 0 0 1 95.6 42.7l5.3-1a91 91 0 0 1 84.9 25.8c15.9 16.9 24.8 40 24.3 63.3l5.6 3.2a112.8 112.8 0 0 1 53.7 84.3 113 113 0 0 1-39.8 96.7 111.9 111.9 0 0 1-70.2 25.3c-76.4.1-152.9.2-229.3 0-57-.3-109.6-44-118.7-102-7.2-46.6 14.8-96.1 56-121.5l3.1-1.9.2-1.3c6-47.4 40.1-90.6 86.4-106.5 13.8-4.8 28.5-7.1 42.9-7.1z"/>
  <path fill="url(#_Linear2)" d="M146.5 234.2H375a81 81 0 0 1 0 162H146.5a81 81 0 0 1 0-162z"/>
  <ellipse cx="336.8" cy="214.5" fill="url(#_Linear3)" rx="59.3" ry="59.3"/>
  <ellipse cx="215.4" cy="215.5" fill="url(#_Linear4)" rx="99.7" ry="99.7"/>
  <ellipse cx="146.5" cy="305.3" fill="url(#_Linear5)" rx="90.5" ry="90.6"/>
  <defs>
    <linearGradient id="_Linear1" x2="1" gradientTransform="matrix(0 512 -512 0 115.7 0)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#343332"/>
      <stop offset="1" stop-color="#111b1f"/>
    </linearGradient>
    <linearGradient id="_Linear2" x2="1" gradientTransform="rotate(-122.2 288.2 99.2) scale(190.98)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0ff"/>
      <stop offset="1" stop-color="#1b8a42"/>
    </linearGradient>
    <linearGradient id="_Linear3" x2="1" gradientTransform="rotate(180 198 107.3) scale(118.628)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#34df41"/>
      <stop offset="1" stop-color="#e9e336"/>
    </linearGradient>
    <linearGradient id="_Linear4" x2="1" gradientTransform="rotate(180 157.6 107.7) scale(199.441)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ff0"/>
      <stop offset="1" stop-color="#fba420"/>
    </linearGradient>
    <linearGradient id="_Linear5" x2="1" gradientTransform="matrix(-181.1 0 0 -181.1 237 305.3)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="orange"/>
      <stop offset="1" stop-color="#fa4231"/>
    </linearGradient>
  </defs>
</svg>
