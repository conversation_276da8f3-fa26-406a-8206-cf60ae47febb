<svg xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="1.4" clip-rule="evenodd" viewBox="0 0 460 341">
  <path fill="#1d1b13" fill-rule="nonzero" d="M190 0a132 132 0 0 1 95.6 42.7l5.3-1a91 91 0 0 1 84.8 25.8c16 16.9 24.8 40 24.3 63.4a112.8 112.8 0 0 1 59.4 87.5 113 113 0 0 1-39.9 96.6 111.9 111.9 0 0 1-70.2 25.3c-76.4.1-152.8.2-229.2 0-57.1-.3-109.7-44-118.7-102-7.3-46.6 14.7-96 56-121.5l3-1.8.2-1.4c6-47.4 40.2-90.5 86.4-106.5C160.8 2.3 175.5 0 190 0z"/>
  <path fill="url(#_Linear1)" d="M120.5 148.4H349a81 81 0 0 1 0 162H120.5a81 81 0 0 1 0-162z"/>
  <ellipse cx="310.7" cy="128.7" fill="url(#_Linear2)" rx="59.3" ry="59.3"/>
  <ellipse cx="189.4" cy="129.7" fill="url(#_Linear3)" rx="99.7" ry="99.7"/>
  <ellipse cx="120.5" cy="219.5" fill="url(#_Linear4)" rx="90.5" ry="90.6"/>
  <defs>
    <linearGradient id="_Linear1" x2="1" gradientTransform="rotate(-122.2 251.5 63.5) scale(190.98)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0ff"/>
      <stop offset="1" stop-color="#1b8a42"/>
    </linearGradient>
    <linearGradient id="_Linear2" x2="1" gradientTransform="rotate(180 185 64.4) scale(118.628)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#34df41"/>
      <stop offset="1" stop-color="#e9e336"/>
    </linearGradient>
    <linearGradient id="_Linear3" x2="1" gradientTransform="rotate(180 144.5 64.8) scale(199.441)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ff0"/>
      <stop offset="1" stop-color="#fba420"/>
    </linearGradient>
    <linearGradient id="_Linear4" x2="1" gradientTransform="matrix(-181.1 0 0 -181.1 211 219.5)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="orange"/>
      <stop offset="1" stop-color="#fa4231"/>
    </linearGradient>
  </defs>
</svg>