html, body {
  margin: 0;
  padding: 0;
  background-color: #343332;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-weight: bold;
  text-size-adjust: none;
  overflow: hidden;
  pointer-events: none;
  user-select: none;
}

.flex {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  -ms-interpolation-mode: nearest-neighbor;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: -moz-crisp-edges;
  image-rendering: pixelated;
}

.sg.top {
  opacity: .6;
}

#rain {
  opacity: .5;
}

#obs .t {
  fill: rgba(255, 255, 0, .8);
  font-size: 12px;
  text-shadow: 0 0 5px #000, 0 0 5px #000, 0 0 5px #000, 0 0 5px #000, 0 0 5px #000, 0 0 5px #000, 0 0 5px #000;
  text-anchor: middle;
  dominant-baseline: central;
}
#obs .w {
  opacity: .5;
}

#datetime {
  font-size: 14px;
  fill: lightskyblue;
  text-shadow: 0 0 4px #000, 0 0 4px #000, 0 0 3px #000, 0 0 3px #000, 0 0 2px #000, 0 0 2px #000, 0 0 10px #fff;
}