<!DOCTYPE html>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Check Weather SG (mini version)</title>
<link rel="preload" as="fetch" href="https://api.checkweather.sg/v2/rainarea" crossorigin="anonymous">
<link rel="preload" as="fetch" href="https://api.checkweather.sg/v2/observations" crossorigin="anonymous">
<style>
html, body {
  margin: 0;
  padding: 0;
  background-color: #343332;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-weight: bold;
  text-size-adjust: none;
  overflow: hidden;
  pointer-events: none;
  user-select: none;
}

.flex {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  -ms-interpolation-mode: nearest-neighbor;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: -moz-crisp-edges;
  image-rendering: pixelated;
}

.sg.top {
  opacity: .6;
}

#rain {
  opacity: .5;
}

#obs .t {
  fill: rgba(255, 255, 0, .8);
  font-size: 12px;
  text-shadow: 0 0 5px #000, 0 0 5px #000, 0 0 5px #000, 0 0 5px #000, 0 0 5px #000, 0 0 5px #000, 0 0 5px #000;
  text-anchor: middle;
  dominant-baseline: central;
}
#obs .w {
  opacity: .5;
}

#datetime {
  font-size: 14px;
  fill: lightskyblue;
  text-shadow: 0 0 4px #000, 0 0 4px #000, 0 0 3px #000, 0 0 3px #000, 0 0 2px #000, 0 0 2px #000, 0 0 10px #fff;
}

canvas {
  opacity: 0;
  pointer-events: none;
}
</style>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" class="flex" viewBox="0 0 400 226">
  <defs>
    <svg id="sg" width="400" height="226" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 452">
      <path fill="#121313" d="M39.1 183.4l14.5 1 4.3-1.6 37.8-31 8.5-9 .2-4-4.6-.8-1.8-3-.5-6.7-8.4-.4-7.4 4-1.6-.7-1.7-4.7h1.5l.6 3.7 1.2 1.1 7.4-4.4 6.8-.4 3.1 1.9.5 6 4.6.8 3.5-1.2 1-3 7.7-9.1V113l1.4-2 3.4-11.5.8-3.7 11.5-5.3-2-.5-3.2-2 1.3-1.5 3.5 1.2 3-1.8-2-1.8.4-2.4-3.3.4-.8-5 3-1.2.7 3.8 1.8 2 5.2-.4 5-7.7-2.1-5 1.9-.8 1.2-5.1 3.4-1.8-1.3-2.2.8-2.8 2.4-.3 2-4 .4-5-3.5-1.7-1.5 5.5-1.3.5 1-5.5 1.5-1.8 5.2 2 1.8-.4 2.3 4.9 1.7-1.8 2.7.7 1.3-3.3 3.7-2.4 1 1.3 5.1.5-.5-4.2 2.5-1 8.2-6.3 1.5-2.2-.5-2.4-6-.2-3.8-1.1-5.2-5.4-1-2.6-3.5 7-.7-.2 3-5.3.7-4.7-7.7-5.5-4.3-.5-4.3 1.7-2 3-3.5 1-3.7-1.6-2.4-3 1.7-1.3 6 3.2 3.3-3.3 4.6-1.5 5.3.6 8.8 5.6 4.7 9.3 4 1.8 7.6-3h6.2l9.5-6.3 8.6-1.7 2.5-2.8L214 9l-6.3 3-2.7-.4L194.5 0h1.8l10.1 10.6 2.4-1.6L214 .3l-10.8.7-4-1h24.3l1.9 13.3 10.5 9.8 6.2 2 .8-3.2 8.6 3.8.5-1.4 20.7 5.4.3 1.5 3-.4 2.6 1 5.7-2 1.1-3.7 3 1.8 2-.8 7.4-5-1.7-4.3 1-1.4 9.4-8 .8.4 4.2-3.3-.7-1.3L316 0h83.4l2 1.8 3.7 1.1 1.2 2.1L418 6.6l7.9-1.3-.2 3 4 2.6 6.4-6.6.8.7-5.1 6.6V15l1.7 4.3 6.3 8.7.9 3 3.7 4-.5.7 4.6 4.1 4.3 7.2 3.8 2.5-1.3 2.6 8.9 3.9v-2l5 .8 12.1 3.7 22.3 3.6.8-4.5L503 55h7.7l9.4 3.7 1.1-.7 6.7 1.4 2.6 2.8 9-1 1.3.5 10.3 2.3 2.4 3 5 1.7 15.8-1.6 2.4-1.5-3.7-7.7 3.4-9.3-1-3.4-4.8-3.5-7.5-1-2.1-2 .8-7.6 1.2 8.2 6.5.5 7.3 4.6 1 4.6-2.3 7.8 3.3 5.5 7.5.5 2-1.7 4.3-.5 3-2.3 3.9 2.2 8.7-7.2 2.2 3.2 2.3.7-1.5-6.5 4.4-4.7 2-1.2 6-5.7 2.6-7.1 1.5-1 1.2-6-1.5-1 2.3-12.9 2.4-6.8-3.6-1.4-1.8-3h61.7l-3.4 1.3-2 4.6-4.6 4.6-5.8 2.8 2.3 6 4.6 5.6 13.7 6.4 6.8-3.3 3.4.6 2.5-2.1 5.2-11.5 6-2.5-5-4.8-.5-5.3 3-.9 5.4 4L718 0l2.5.3 6.4 7.4-1.4 7-2.3 1.2-7-.4-3.4 2-4.3 10-7.8 7.4-1.3 5.8 5.2 2.8 14 2.8L735 56.7l1.7 2.2 9.4 6.4 4.3 8 5.6 3.4 5.2 5 .3 7.4 3 4 2 5.4 16.9-12.4 2.4-.4L792 87l8 6.7V99l-8.8-5.3-2.4-.5-4.4 1.8-3.7 4-1.7 4-4.3 15.8-7.2 2.3-6.9 7-3 1.3-1.6 2.8-2.6.5-2.8 5.2-3.3.3-.2 3.6-2.3 2.7-1.1 3.5-2.8 2.5 2.5 1 4-1 21.3 5.9 3.6 3 21.6 8 6.1 3.1-.4 251.4-1.6-3.7-5.1-4.2-1 .8-3.6-1-17.6-9.2-6.7-5.3.5-2.9-2-3-1.1.6-5.6-1-2 2.4-2.6.3 2 5.2-.6 3.8-6.7 2.2.2-2 5-.3v-5L749 398l-10-2.7-4.9 1.2.4 2.6-.8 2.4-7.7 4.3-7.4 1.8-.4 1.8 1.8 1.6-1.3 4.3-2.2 1.7.2 1.9-4 8.4 1.2 2.7v6.7l2.9 3.8-.6 3.5 3.8-.2 7.2 5 6.2.9 1.8 2.3h-36.8l.2-4.1-6.2.5-3.2-2-2.4-.2.2-5.7 4.3-4.1-6.2-4.3-4.6 1-3.2-2 2.2-4-2.2-.8-2.8-5.2-7.5 3-4.6 4.5-2.3-2.8-10-.5 1.2-4.7-1.3-2 .7-6.4 2.8-1.4.4-4.4-2.5-5-2 4-8.1-.8-3 1.8-7.6.5-2.2 2.9-2.6-.1-2.1-2.2-3.7 2.7.7 1-7.5 9 4.2 1.7-.2 1.4-3.5.2-.2 3.1 7 1-.5 4-1.9-1.3-4.5-.3.4 5.4h4.7l.1 3.8-5.7 1.3 1.3 2.5-3.6 2.7 1.7 5.8-1.9.5H482.4l-9-12-4.2-2.4-1.4.4 2.2 7.8 1.8 5.1-7.8.8-3-2.4-4.2-1-3.3 1-.2 2.7H444l8.1-5.5-.7-2.4-4-1.6-2.2-2.6-3.8 1.4-.1 4.3-2.6 2-.6 2.5 2 1.9h-14l3.2-8-8.3 6.5 1 1.5H0V181.4l3.6-2.5 24.6 5.4 3.3-1 3.4 1.3 4.1-1.2h.1zm-7.7 3.6l.4 8.1 3.8 9 1.5-.4 9.3-9.8 2.3-1.5 1.3-3.6-.6-1.5-7.5.6-1.1-1.1-9.4.2zM57 327.3l.5 4.2 8-.3 1 5.5 5.9.2 3 7.3-.7 6.9-3.3 1.3-.2 1.4-7.6 4.5-1.3 4.8 45 11.7-.1-.8-41.4-10.8-1.6-1.3L66 358h3l2.9-1.9 2.3-4 2.3-1.3.6-3-2.7-9.6 4-1 1.9-2-8.8-18.7v-3.8l19.2-9.4-.4-.6-10 4.9-.2-5.5 24.2-11.8.9.2-.4 5.4.6.5 2.7-28 1.5.4-1.4 3.3-.2 5h8.3l4-4.5-.1-4-16.6-31.3-4.3-5.4 3.2-6.8-3.6-4.3 18.6-19.6L99 221.5l7 6.2.7 1.3 4 3 4-3.4 5.3-5.3 3.2 3.2-8.3 8.7 5.1 5.8 6-7.6 4-1.5 1 2-5 5-5.9 6.7 1.4 1.7 3.6-3 .6 2.2 6.3 5.4.8 2.7 8.5 4.9 2.4 4.2 11.2-9-7-7.3-1-8.4 5.6-.7.7 6.5 7.7 7.8 4.6-1.8-1.4-13.4.6-3.6 2.4 1.7 1.2 13.8 14-2.6.3-7.9 2-2h3l2 3.5 4.9 2v3l9-.2-.3-8 4.1 1.3.7 10 12-.3.8-1.8-7.9-6.8-4-1-.5-3 5.1.5 9.6 7.9 7.9 3.7-3-7.2-2.7 1-1.2-1 4.4-11 9.7-17.6-2.1-4.4h-4.7l-1-1 1.9-7.6 3.2.3 2.7 2v-3.3l-3.5-2.4-3 2.6-1.8-3v-3.2l-3.3-2.5H224l-1.8-1.5 1.3-2 2.9-.3 3.6 1.6 1.2-1 3 1.4 4.2 7.8 1.5 6.5-.5 1.5-5-3.4-3 5.4.5 1 5.3.2 2.1 5.4-2.5 9-6 9-1 3 .7 3.7 2.5 8.5 16 4.6 3.3-3.4 2 4.7 6.6 2.2 8.7-1.2 3.3-4.1 7.5 7.9-3.3 5.4 16.4 9.8-4.5 7.6-18-10.6-4.7.7-4 6.9 29 17.3 10.6-18 3.4 2-1 1.8 10 6.4-12 20.6 9.9 6 14.3-23v1.6l8 5-.3 1 5.9 5 2.2-5.5 2.5 1.7h2.4l2-2 3.3 1.4 3 2.2h4.3l1.1 1.1h6.6l10.7-6 10.7-.4 8.7 8.4 1.4-1.5-3.3-4 1.7-1.8 8 8.7 1.1-1-.7-3v-13.2l9.7 5-.7 1.4 4 5 1.3-.8-2.5-3.5 17-13.4-.7-4.6-5.7-4.9-11.5-4-5 9.4-2.7-3-.3-3.3 1.4-3.5-1.3-2.5 15 .4 1-1.8-1.3-11 1.2-2.4 3.6-2.8v-6l3.3-4 .2-3.4-1.7-2.4.6-3.7-3.4-6.1.8-.5-.3-6.7-4.8-6.6 1.2-5.9-.6-4.7-9-7.3L413 178l-10.5-12.3-.4-2.3-2.9-1-1-1.7-13.2-3.2-9-4.6-9.2-.4-1.3-1.2-6.4 4.7-2.7-.2 5.6-4.1.6-2-6.2-3.2h-5l1.4-2.8 3-1 3.5.8 3.4 2.9 1.2-2.2h2.2l3.8 4.4-1.3 4.2 10 .7 6.9 3.8 13.3 3.2 1.3 2 2.4.7.3 2.3 10.7 12.6 1.3 10 9 7.1 1 5.6-1.3 5.6 4.6 6.3.8 9 2.8 4.4-.8 3 2.2 3.6-.4 4.7-2.5 3 1 1.5 3.5.8 2 5.2 5.7 1 10-8.7 8-2.2v.8l-7.8 1.7-7.6 8-3.3 2-6.7-1.5-2.1 1.4h-6.2l.4 6.8 3.4 7.7 6.7 8.2 3.1 2 6.3-4.6 5.4-4.4 1.7-.4 4.1-3.8h2l6-4.9 3.8-.2 6-3.3 16.3-4.5 6-3 16.4-3.5 6-2.4 1.8.7 21-7.9 9.7-1.6.6.7 10.4-4.8 2.1.7 8.5-2.6.6.6 6.7-1.2 1.5 1.7 2.5-1.9 7-.4 18.5-42.3-17 40.8 1 6.4-2 1.8 9.6-2.7 8.8 2.8 1.5 2 7.5-.2 8.2-5 2.6.3.8-1.7 16.4-.6v-6l8.1-.2.2 6 5.5-.4-.5-12.4h-13l-2.6-2-.7-2.6 1.5-3.7.2-4.9-16.2-6.8.7-.6 12 5.2 6 1.2 1.3-1.8 4-12.3 6-12.4 1.5-7 7 2.2 4.4 3.5 1.5 3.6 2.4-1.5-3.4-2.7-12-8.6-15.9-7.7-8.6-2.6-2.5-2.6-5 2-6-3 .8-2.3 3.3-1.7-4-1.7-5.6-12.8-5-7.4-16.2-6-3 1.2-3.7-.6-7.8 1-4.4 2.6 1 4.3-4 .7-6.3 5 .4 3.1-3.5-1-6.6 1-8.8-.4-6.6-2-16-9.8-6.6-11-4.7-2.8-.5-2.6 1.8-1.6v-1.7l-21.3-19.4-3 1.5-.2-3.2-2-3.9h-6l-2 1.5-5 .4-6 4.5-1.4 3-2-1 1.8-4.2L470 77l-11.5-7-1-.2-11.2-7.6-4.4-1.3-3.8.7-5.4 3-2.1 3.2 1.6 2.2-1.6 1.4h-2.4l-6 2.7-7.7-3.5.5-3.8 2.5-1.4-.2-1.8 3-2.7 1-3-1.9-4.4-5.5-6.3h-2l-4.7 5.4-.5-1.7 6-6.5L411 39l-1.5-1.7-2.8-.5-5.1 2-4.3-.4.3-8.8 1.5 5.6 1.6 1.4 5.7-3.6-2.3-5.3-7.8-4.3-16.3 10v-.6l16-10-12.6-7.6-4.2 6-1.3-.6 3.4-6-9.6-5.5-4.3 7.5-.6-3-3.2-1.7-3.8 3.4-1.6-.4 6-6.4-6.8-2.4-13.8.5-4.2 1.1-9 4-30 22-9.4 3.4-1-1.3-4.3 6.7.3 1.8h-3l-3.7 8-1-.7-10 .3-2.8 1.2-4.1.8-8.4-4.7-2.2 2-5.5-.2-1.4-5-5.4-3.4-2.1-3.6-2.7-2.7-3.4-.6-5.5 2.5-1.8-.5.5-2.6-2.2-1.4-8 1.5-5.8 2.2-7 5-6 6.2-3 1.6-6.6 6.6-3.7 2-6.8.7-3 1.5-.4 2.1-3.9-1-3 3.1H160l-2 4-3.2 2-1.5 5.6-1.3 2.5-.8 5.7-1.2 2.3v3l-4.5 3.6-3.2-.5-1.7 4.2-2.6.7 1.7 4.7-1.7 5.1.7 1-.3 7.5-2 6.2-1.8 3.2-3.7 2.5v2.7l-6.5 11.6-.8 2.2-3.8.2.3 1.7-8.5 15.7-4.4 3.4-3.7 9.2-1.7-.5-3.7 3.7.6 4 2.6.9-3.3 5.3.8 1.6-22.5 62-1 .5-.8 4.3 4.9 8-.9 4.8-4.2 7.5-4 2.2-3 .2-1.6 6.3-4.3 6.6-1.1 5.3 3.7-4.9h3.6l1.7 1.4-1.3 2.7 1.6 13.7-2.5-.4-.3 4h-4.4l-1 1.6 1 8h4.5l.7 1.5-2.6 2.1-7.7-.3zm72.2-37.8l2 4 2.2 1.7 2.2 4.6.2 3.5 3.3 7 3.6 6 1.4 1.6 3.3 7.5-.1.8-5.7 3 5.2 11 6.2.8 4.2 9-.2 1-5.1 5.2 2.6 1 21.7-22.2 1.3-4.5-6.4-22.6-8-7.5 6.6-7.7 6.7 6 5.7 5.1 3.6 13.6L201 312l9.8-5.7.2-2L198 297l2.6-4.7 5.7 2.6 10 5.6 4 3h2.2l6-6 3 1.1 1.3-2.9-.7-1-5-2-3-.4 4.5-4 3.2 1.2 4.6.8 1.3 1.4 7.8-3 2.6-3.4-6.3-6.3-1.9-1-3.6-.1-7.6-7.4-.7-4.6-1.7-2.9-4.3-2.5-2 .2-3.3-1.2-.8 1.8-7-1.5-8.5-1.1-3.8 4.4 3.4 2.6 1.8 3.3-1.7.8 1.8 6.2 1.4 1.9 2.6.3 1 1.2-3.7 3.5-2.7-1-3-3-1.2-4.7-3.6-4.4-6 3.3-1 3.4-1.6.5.6 2.7-2.8 2.3-1.7-1.4-1.7 1.6-3.6 5-6.2-5.7 3.6-3.1 5.8-6.2 1.2-3.6 5.9-6.7-.3-.6-11 .1-5.5 1.6-11.4 7-27.5 19.1v.5zm54-49l1.2 2.9.6 5 5.2.3.2-5.5-4.5-5-2.6 2.3zm17.9 142.3l.8 1 11.7 2 2.6.3 4.5-1h3l5.4 1 9.6-2.2-3.1-2-5.3-4.4 2.4-1.5-2-1.3-3.2.6-2.6-1.3-.4-2.7-3.1-1-4 2-7 1.5-3 3.9-5.7 3.8-.6 1.3zm14.5 26.2l1.7 1 1 6.7 3.7-1.7 3-2.4 5.4-.6 1-4.3 2.8-.2.7-2.4-8.2-2.3-2.7.5-1 4.3-3.6 1.3h-3.8zm8-371.8l1.8.2 5.5-2.7-5.3-1-1.8 3.5h-.1zm6.7 156.5l1 1.5 4.8-1.9-1.8-3-2.4.5-1.6 2.8v.1zm1 234.3l2-.2 2.7 2.6.9 3 2 2.5 2-.4 4.2 1.8 2-1-.5-3.7.4-4 2-2-2.8-1-3.5.4-5-2.3-5.2 2.2-1 2-.1.1zm15.7-87l1 2.5 3 1.7 4.6.2 9 2 10.6 6 10.4 1.3 3-.4 2.7-3.4-1-1.7-4-2.1-5.1.2-5.2-1.6-1.4-2-1.6-5.5-2-.5-3 3.3-4.7-1.9-2.1-2-3.3-.6-5.5 1-5.4 3.5zm10.1 13.2l2.7 2.8 2.4-3.5-4.4-1.5-.7 2.2zm3.5-49.9l2.5 2.3 6.4 3 3.9 2.3 2.6-1.4-1-1.8-3.5-3.5-3-2-4.1-1.7-3.8 2.8zm12.2 77l1.5 6.9 2.7 5 3.7-4.5 4-2.5 8.2 5.7 4.4 2.1 6-2.7 2.4-2 1-6.5-.7-6.5-2.4-2.2-6.6.4-8.3 3.5-4.5-1.7-2.3-1.8h-2.2l-2-3.8-2.3-2.6-1 6.7-1.3 2.8-.3 3.8zm2.6-44.9l1.6 7.3 3.8 1.8 5.5.7 4.8 1.8 2.3 2 1.3 4.4 8.3.4-2.7-6-4-4.2-3.5-2.7-1.2-.1-4.4-4.6-4.3-3.7-4.2-1.3-2.6.8-.7 3.4zm45.6 42.2l2.7 4.9 6.8 4 3.2 1.1.7-1.6-4.2-4.4-4-5.1-4.3-3.2-1 4.2h.1zm21-73.3l4.5 5.2 1.4-.6 3.8 3.4 3 2 2.3 2.8 3.6 2 1.2-1 5.8 4.6-1.7 1 3.7 3.7 2.6.4 2.8 2.3.6 2.3 3.6 2.6 3-1.8.6-3.6 4-1.9.3 3-2 1.2.4 1.8 3.5-2.4-.5-3 1-3.2-.2-2.2 1.7-2.5 1.2 7.3 3-3 5.5-8.1.5-1.8-2.8-1.6-6 1.4-1.1-2-4.4-.6-1.7 1.3-3.8-.3-2-1.5-3-.4-5.3.2-2.8-1-2.8-3-7-3.7-2.6.2-2.6 1.3-3.2-.2-5-1.5-3.1 1zm4-6.5l1.7 1.6h4l-2.1-2.8-1.3 1.2H346zm21 5.6l3.4 3.5 4 1.7 1.3-.2 8.5 2.6.3-1 4.5-2.2 3.9-1.2-9-8.5-8.2.3-8.8 5zm13 66.1l2.2.9 1.8-2.8-1.6-1-2.4 3zm13.8-12.3l7.1 7.6 1.6 2.5 2.4 1.3-.8-8 3-4 5.5 6 2.5-4.5-3-2.9-.3-3-2.6.3-.9-4.3 2.6-.2 3.1-1.3 1.2-2.2-2-1.3-1.5 2.6-2.6-.6-.9-1.6-3.2.9-1.8 1.5-1.7-2.1-1.4 2 2.5.9-.5 2.2h4.4l.2 7.4-2.8 3.2-8.5-4-1.6 1.6zM413.4 36l1.5.7 2.5 4.7.2 3.3 2.4 2.5 3 4.7 1.7 1.4 1.4-1.5-.2-2.3-6.4-9.7-4-3.9h-2v.1zm147.8 64l-3.2 2.2-1.4 1.7.2 2.8 9-2.7.8 1 4.4-.4 1.1-1.7 2 .6 3.8-1.5 2-4.2 5.9-3 3.7 1.6 2.9-.7 1.2 1 3.2-1.1 8.4-.8L603 93l.7-6.1 1-1.7-1-2.5-2.2-2-5.3-1-3 1.4h-3.7l-2.4 1-5.6-2-.7 3.8-1.8 1.3-1.2 5.6h-3l3-.7h-3l-1.4-2.4 1.1-1.4 3.3-.1-.4-1.8 2-4-5-2-6.7 1-5.5 2-3.3.4-.4 2 2 .8 3-3 1.3 5.8-1.8-.4 1.3-3-1.3-1.4-3.3 2.6-2.2-3.4-3.2-.4-2.8-2.5-2.5 1-5.3-2v-2.3l-4.8-3.2-2-.2-11.9-4.7-2.9-.5-1.5 2h-3.2l-1.6 2.7-3.7 1-.1 2 2.4 4.3 3.4 2.8 4.7-1 .3 3 7.4 1.8 2.6 2.7L538 90l3.5 2.3 4 4 2.2-1 4 4 1 2.5 3.9.3 5.1-4.9-.6 2.6h.1zm-22.4-4.6l4 4 2.4 3.8 3 2.7 2.8-1.2-1.4-2.8-6.4-2.5-2-3.5-2.4-.5zm42.4-28.5l2.2 2.5 2.4-.8v-3.3l-2.6-.3-2 2zm47.4 10l.4 3 4 11 3.4 2.7 11.6.5 2.5-.7 3-3.6 3.5-.8.8 3.2-1.8 2.8.2 1.7 2.4 3.2-1.9 2.2-4.4 1.8-13.6-.3 2.4 3.2 2.5 1.6 6.8-2.7h4l3.1-1.6 3-3.3 4.4 1 3.4 4.3 1.6 6.2 3.4 1.6 4.2-.2 4.6 1.6 10.6-5.7-.4 1.1-10.2 6-4.6-2-3-.2-1 1.4 4.9 5.7 5 7.1 3.8 4.7 1.4.3 1.7 4.6 1.6.5 4.7-2 4.4-1 1.8-1.2 10.4-4.2 4.8-2.3 7-2 2.6-1.1 1.2-1.6 1.2-5-.2-3 2.1-6.6.2-3.6 1-1.5 1.3-7-1-.8-5 1-6.5 2.7-6 4.8-1-.9 6.4-4.8 7-3 7.5-1.3L737 92l-2.6-4.4-1.3-3.3-3.3-5.2-4.2-2.8-7.2-6-4.2-2.8-6.7-2.5-9.3-4h-3.4l-2.6 3.3-2.2 1h-3.3l-1-1-5.3-.3-2.3-2-3.4 3.3-.8 2-5 3.5h-9.3l-1.5-1-1.7 1.1v4.7l1.4 4.3 1.4 2.5.8 5.5-5.6.2v-2l-1.6-5-1.9-2.9-5.2-2-1.8-3.6-3.1-3.2 1-1.6-2.8-3-2.8.4-5.2 5-1.3 1.8-1.1 5v-.3zm14.6-17.6l2-.2 3.3-3 5 1.2 6-1.2 3.6-1.6 1.4.3 4.8-1.2 1.7 3 8.5-.6 2 .3-2.7-5-2.5-2-6.4-.7-6.2 1.5-14 4.2-3.9 2.2-2.6 2.8zm20.5 78.2l16.2 14 1-1.3-8.4-7.7-7.3-5h-1.5zM712.2 3.8l2.3 5.2 4 2.4.3 1.7 3.5.7 2-1.4.8-3.9-.4-1.7-3.2-1.7-3.1 1-3.8-4-2.4 1.7zM270.4 250l-4-9.3-1.5-2.7-2.7-2-4.7 1.2-2.4-1 3-2.8 6.2 1.1 4 4 4.3 9.6-2.2 2v-.1zM60.4 71.4l1.2-2 2.5 1 .4 2.3-4-1.4-.1.1zM71 300l3.9 1v2.6l4.4-1.2-.4 5.9-5.2 2.5v-5.8l-2.7-5zM93.7 35.5l5.6 1.5-1.5 2-3.2-1.8-.9-1.7zm5 59.3l1.5-.8 2.7 3.2h2.8l-1.3 3.4-4.5-2.9-1.3-3zm4.7 84l4-11.8 4-3 9.2-17.1 1.5 1.8h2l3-3.1-1.5-3.2 6.3-12.6 1-.8 2.8 3 1.5-1 2.2 1 6.7-2.2 7.3.2.7 3 3 2.2 2.9-1 1.7-4.6 3.2 1.7v2.1l-2.7-.9-1.5 3 1 1.3-7.7-.7v3l-4.6 2.3 3.7 4-.5 1.9 3.9.4-2 1.9-.5 3.8-3.4.7-.4-2.3-2-2.1.8-1.5-.7-3.5-17.6 1.7-1.7-1.4-5.2 6.7v2.3l2.4 2 .3 4.4 2.7 2.6-4 1.7-1.5-5.5-10.4 2.4v4l-5.7 2 4.6 7.8L123 185l2.8 9 2.5-1.7 2.3 2.2-5.3 3.2-2.2-.8-3.4-5.3 1-2.8-1-2.8-5.3 1-4.6-5.6-6.5-2.5zm43.2-77.6l3.9-6.1L153 93l4.6 3.8h2.8l.8-2-1.6-3.4 1.4-.8 2.2 5.6h3.6l.4 1.1-4.2 3-1.7 2.7-4-1.3.6 3.4 2.2-.6 1.6 4.7-2.7 2.4-1.5 6.7-3.6-4.8-4 5.8-.7-2.5 2.2-2-.5-2.6 1.7-.9-.4-3.2-5.6-6.9zm11.6-30.5l.8-1.6 2.3-.7 3 2.4 2-2-2-3 2.6-2 1 1.6 2.7.3 2.4 1.5 4.2.4 4.8 4-1.3 1.2-5.7-3-4.7-1-.5 6-2-3.4-1.4.7 1 3.4-4.7.1-1.5-2.7-3-2.2zm2 45.9l1.2-2 2.4 3.3-2.5.7-1.2-2h.1zm1-11l2-2.3 1.4 1.7 2.8-.8-.6 2.5-2.2.4-.4 3-2-1.5-1-3zm2 10.3l2.3-1.2 3-3.6.3 3.6-1.2 2.3-3.5.8-1-2zm5.5.7l.7-2 2.6-.6-.3 3-3-.4zm20.9-54.4l3.3-5 1.2 2-3.2.4-1.4 2.6zm3 20.7l6 5.6 10.1-.6 2.7-2.8 2.2-.4 4.7 4.3 3.5.4 4.5-1.3-.5-3 4.7.8 2.5-.7 9-8.8 1.1-10.2 1-2.4-3-1.4 1.8-2.3 2.6-1-.7-3-2-1.7.1-2 8.6-.2 4.7 7.8 1.3 6-9.4 11 3.1 9.4-.7 7 3 3.7 9.8 1.6-.2.7-7.4-.7-5-3-1-3.5.6-6-2.7-6-2.6 1.6-.5 1.8-2.8-.4-1.3 2.5-2.6.7-.8 4.5-3 .6-1 2.2 3.7 7.7 2.5 2-.6 1.6 4 7-2.2 8.5h-.8l-2.1 13.2-1.3-4.2 2.7-10.4-.3-6-1.5-2v-2.8l-5-7.2-3 3v7.8l-3.2 4-5.1 2-3.6-1-4.5-5.6.6-.5 3.7 5 2.1 1 5.9-3.7.8-3.4-2.2-1 2-3.2.4-4.7 2.3-3.2V94l-2.5-1.5-2.3 3.6-.2-4.7-8.9 2.8-.4-4-3.4-1.7L208 93l-2 1 .3 4-2.5.7-4.7-3.5 7.5-3.4L205 90l-4.1-.4-4.1 1.5.2-3.5-4.4-4.7zM241.2 230l.8-6.5 2-3.5 14.6 2.6 1 2-2.6 4.3v3l-2.3 4.8 2.2 1 3.5-1.4 1.4 2.5-2.6 3.3h-5.5L241.2 230v.1zM254 186l5.3 3 1 2.2 2 19 3 5.8-6.8 14.6-1 .2 7-15.4-2.6-5.6-2-19.7-5.9-4.1zm12.7-16.9l1.9-2.6 1.8.4-1.7 3-2-.8zm14 214.8l3.5 2-3 1.7-.5-3.7zm5 2l1.6-2 4.1 1.6-2.2 2.8-3.6-2.5.1.1zm4.2 2.7l2.6-3 2.3 1-1.7 4.4-3.2-2.4zm4 2.6l2-4.6 8.7 2.7-6.8 4-4-2zm5.7-278.4l6.3-3 2.3.7 4-.3 2-3-2.7-2 .3-1.7 6 2.6 4.2-.2 5.5-1.2 2.3-4-3.4 1 .7-3.6-2.9-1.1-1.8-1.8-4.1-.2-4.3 3-2.7-2.3 4.4-.6.7-1.2L314 91l6.2 1.4.3-4.3 2.1 2.7 2-1.1 1 4 3.5 1.7 3.2-4.1-.7 5 2.7-1.4 1.8 3 2-2.5-1-2.1-2.2-1.5 1.6-6.6 3 2.8-1.7 1.2.5 2 3.3 1.3 1.8 5.6 1.7 1-3.6 6.3-6.6 6.4-3.2.6-1-1.7-3.6 3.4-2.4-2-4.8-1.2-.7 2-2.2-.8-2.6 1.9.5 2-.8 6.5 1 2.7-1 3.4-1-9.7-2.4-1.2-3 6.6v2l-2.6 4.8-1-1.2 2-2.1 1.6-8.5-3.2-1.7 5.8-3.1-.5-2.9-10 1.8v-.8l-.2.1zm1 273.9l2.9-2.7 2 .9-.7 3.6-4.1-1.7v-.1zm5.5-229l3.3-.4 2.2-1.6 4.8-1.7 1.4-4.4-3.5-.8-1.8-1.4 1-2.8 6.7 1.3v-3l2.4-.9 2-7.8 1.7 11.2 3.4-2.9 3 6.6 1.8-1 4.4.4 1.4-2.5 4.4-6 2.7.5 1.7 3 1 7.3-1.3 3.4 1.2 5.4-4.2-5.7-.5 3.6-1.8-2-2 .4.3 3.3-2.3-.3-1.9 3.4-3.2-.6.8-3.5-3 1-8.5 6.7-1 1.5-5-2 5.8-3 .5-2.5 2-.1 4.3-3 1.8-2.6-.6-2-5.5 1 .6-2.3-2-.5-1.3 2.4-1.7-3.2-1.7 3.2v2l-2.7 2-4.4.5-3.7 1.5-3-1.1zm5.4 16l1.3-.8 2 5-1.1.7-2.2-5zm37.4 6.6v-1.6l5.9 4.5 1.6-4.2 1 4 4.1 1.3 3.1-1.8h5.8l2.5 1.5 1.7-.6-.6-3.7-3.9-1.1 5.3-3-.7 2.6 1.8 2v3.3l4.9-.5 1.7 3.4-4.9 2-3.3-1.4-.5 2-5.4-3.7-6.8 1-1.3 3.6-3.6 3.1 2.1-5.6-3.3-1-5-2.2.3-1.7-2.5-2.2zm8.5-74.9l2-.7 3.1.5 3.6-2 6.2-1.7 2-7.5 6.9-3.2 2 1.9h5l4.7 2.8 5.8-1.6 1.1-2 3-1.4-2.8-4.2 3-1.8 2.5.6 1.2-1.3 1.4-5 3.8-3 1.6-4.8 8.9 4.1-3 6.7-18.9 17.6-4 1.5-.1 5.1-1 .5-3.4-3.3-1 4-2.4 2.2-4 .3-3.8-2.2 2-2.3-2.3-1.9v-2l4-3.5-1.4-1-6.4 1.6-3.5 4.4-1-.7-6 1.6-4.3 2.2-4.5-.5zm42.8-18.2l2.3 1 2.2-2.7-1.2-.7-3.3 2.4zm-15.1 229.2v-1.7l5.3-.4-.2 3.9-1.5.8-3.6-2.6zm6.9 3.8l2.4-2.6 3-2 1.8 2.2-5.1 5.2-2.1-2.8zm2.2-113.4l7-.6 9.2 1.1-16.2-.5zm33.4-65.9l4.7-1.6 4.2.1 4.7-5.4 1.4-4.8 4-3.7 1.5-5.6-.3-4 5.2-5.3 2.4-9.7 6.6-5 5.2-6.5-2.4-3-7.6-1.3-5.7-2.7-3 .4-1.4 6.7-3.3-.7 3-4.3-8.2-6-1.8-4.7L432 71l.6-.7 9.6 8.4 7.7 2.5 5-7.6.8.5-4.8 7 7.1 3 13 2.2-2.8 5.8 9 3 1.7-1.5-.2-4.2 4.7 1.3 2.5 2 .3 3.3 3.7 1.3 16 12.4 5-3.5-1.7-1.8-.5-2.5-4.2-2.4-6-5.4-3.3-6-.8-4.4 1.2-.1 2.1 6.2 3.3 4.7 4.6 2.6 3 3.2 5 1 2.5 3 .4 2.8-7.4 2.7-6.7 5.7-8.4 12.2-1.7.6-4-1-4.6 1-1.6 1.6-.5 10.6-4.1 5.9-3.2 7h-.7l2.8-6.6 3.5-4.4 1-6-.9-1.6 1.2-5.6 2.8-2.3 4.2-.8 4 .8 1.8-1 3.3-7 8-9-7-6.3-12-7.4-1-4-2.6-1.7-3.8-.5-.7 5.7-5.8-2.7-5-.1-4.4 4.8-5 3-4.6 12.6-4.4 2.8-.3 4.6-2 4.9-4.3 4.2-.3 3.3-5.6 6.6-4.3-.2-4.6 1.4zm74.6 45.3l1.2-2.4 7.8-1.7.3 2.2 4.5.6 1.4 3.4 3.1 1.5.8 3-1 .8-6-2-3.8 1.1-5.2-1.2-3-5.3h-.1zm.7-10.4l2.7-4.9 3 .2 2.6 2-1.4 4.2-2.6 2.3h-2.4l-2-3.8zm34.4-88.4L542 86l2.5 1.2 4.7-.5-2.2 3.6-3-2-4 1-2.7-2zm10-40.2l2.4.3L551 45l1.9 2.1-3.5 1.1-2-1v.1zm2 35.9l2.3-2.3 1.4 2.4-2.2 1-1.3-1.1h-.1zm3.3 13.2l3-3.2 3.8-1-4.4 5.3-2.4-1.1zm14 5l2.8-1.2v2.7l-2.8-1.5zm14.2 86.3l2.2-5.3 1 .5-2.3 6.7-1-2zm14.8-101.4l1-3 1.4.2 2.6-2.2 1.8 2-3.8 3.2-3-.2zm75.6-4.8l2.8 2 3.3-5 1.8.4 1.8-1.3 6 1.7-2.7 2.4-3.8.8-.4 2.8.5 3.3 5 1.5-1.9 1.9-5-2.6-2.8 1.3-2-.4-2.4-3-.2-5.8zm18.4 366l2.1 1.2h6l-1 3.2-2-2.7-3.7 2.4-1.4-4zm63.9-12l1.8-3 2.3-1 1.8 1.3 6.5-2.4 4 1-8.6 2.3-.4 6.6-2-.1 1.8-3.2-1-2.3-2.8 1.8-3.4-1z"/>
    </svg>
    <svg id="w" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="40" height="40">
      <path fill="#fff" d="M413.1 222.5l22.2 22.2a23.9 23.9 0 0 1 0 33.9L241 473a23.9 23.9 0 0 1-33.9 0L12.7 278.6a23.9 23.9 0 0 1 0-33.9l22.2-22.2a24 24 0 0 1 34.3.4L184 343.4V56a24 24 0 0 1 24-24h32a24 24 0 0 1 24 24v287.4l114.8-120.5c9.3-9.8 24.8-10 34.3-.4z"/>
    </svg>
  </defs>
  <use class="sg" xlink:href="#sg"/>
  <image id="rain" width="400" height="226" preserveAspectRatio="none"/>
  <use class="sg top" xlink:href="#sg"/>
  <g id="obs"></g>
  <text id="datetime" x="16" y="24"></text>
</svg>
<canvas id="rainarea"></canvas>
<script>
const $rain = document.getElementById('rain');
const $obs = document.getElementById('obs');
const $datetime = document.getElementById('datetime');
const $rainarea = document.getElementById('rainarea');
const HOST = 'https://api.checkweather.sg';
const lowerLat = 1.156, upperLat = 1.475, lowerLong = 103.565, upperLong = 104.130;
const longRange = upperLong - lowerLong;
const latRange = upperLat - lowerLat;

// import arrowDown from '../assets/arrow-down.svg';

const calcPos = (long, lat) => {
  // Note: These are inaccurate measurements.
  // Maybe only works for Singapore since it's small.
  return {
    x: (long - lowerLong) / longRange * 400,
    y: (upperLat - lat) / latRange * 226,
  };
};

const timeID = (id) => (id.match(/\d{4}$/) || [''])[0].replace(/(\d{2})(\d{2})/, (m, m1, m2) => {
  let h = parseInt(m1, 10);
  const ampm = h >= 12 ? 'PM' : 'AM';
  if (h == 0) h = 12;
  if (h > 12) h -= 12;
  return h + ':' + m2 + ' ' + ampm;
});

const intensityColors = [
  '#40FFFD',
  '#3BEEEC',
  '#32D0D2',
  '#2CB9BD',
  '#229698',
  '#1C827D',
  '#1B8742',
  '#229F44',
  '#27B240',
  '#2CC53B',
  '#30D43E',
  '#38EF46',
  '#3BFB49',
  '#59FA61',
  '#FEFB63',
  '#FDFA53',
  '#FDEB50',
  '#FDD74A',
  '#FCC344',
  '#FAB03F',
  '#FAA23D',
  '#FB8938',
  '#FB7133',
  '#F94C2D',
  '#F9282A',
  '#DD1423',
  '#BE0F1D',
  '#B21867',
  '#D028A6',
  '#F93DF5',
];

let nowID;
const showRain = () => {
  fetch(HOST + '/v2/rainarea').then(res => res.json()).then(body => {
    const { id } = body;
    if (nowID === id){
      setTimeout(requestAnimationFrame, 60 * 1000, showRain); // every min
      return;
    }
    nowID = id;
    $datetime.innerHTML = '';

    const { radar, width, height } = body;
    const canvas = $rainarea;
    const ctx = canvas.getContext('2d');
    canvas.width = width;
    canvas.height = height;

    const rows = radar.trimEnd().split(/\n/g);
    for (let y = 0, l = rows.length; y < l; y++) {
      const chars = rows[y];
      for (let x = chars.search(/[^\s]/), rl = chars.length; x < rl; x++) {
        const char = chars[x];
        if (char && char !== ' ') {
          const intensity = char.charCodeAt() - 33;
          const color = intensityColors[Math.round(intensity / 100 * intensityColors.length)]
          ctx.fillStyle = color;
          ctx.fillRect(x, y, 1, 1);
        }
      }
    }

    // const path = 'https://www.weather.gov.sg/files/rainarea/50km/v2/dpsri_70km_2019021805500000dBR.dpsri.png' + '?' + (+new Date());
    // const path = `https://www.weather.gov.sg/files/rainarea/50km/v2/dpsri_70km_${id}0000dBR.dpsri.png`;
    // $rain.setAttribute('href', path);
    // $rain.setAttribute('xlink:href', path);
    $rain.setAttributeNS('http://www.w3.org/1999/xlink', 'href', canvas.toDataURL());
    $rain.classList.remove('loaded');
    setTimeout(() => {
      $datetime.innerHTML = timeID(id);
    }, 1000);

    setTimeout(requestAnimationFrame, 60 * 1000, showRain); // every min
  });
}
$rain.onload = () => {
  setTimeout(() => {
    $rain.classList.add('loaded');
  }, 2000);
  // setTimeout(requestAnimationFrame, 60 * 1000, showRain); // every min
  // $datetime.visiblity = '';
};
$rain.onerror = () => {
  nowID = null;
  setTimeout(requestAnimationFrame, 1000, showRain);
};

const showObservations = () => {
  fetch(HOST + '/v2/observations').then(res => res.json()).then(body => {
    $obs.innerHTML = '';
    body.forEach(f => {
      const { lng, lat, temp_celcius, wind_direction } = f;
      const pos = calcPos(lng, lat);
      if (temp_celcius) {
        $obs.insertAdjacentHTML('beforeend', `<text class="t" x="${pos.x}" y="${pos.y}">${temp_celcius}°</text>`);
      }
      if (wind_direction) {
        $obs.insertAdjacentHTML('afterbegin', `<use xlink:href="#w" class="w" x="${pos.x-20}" y="${pos.y-20}" transform="rotate(${wind_direction}, ${pos.x}, ${pos.y})"/>`);
      }
    });
    setTimeout(requestAnimationFrame, 2 * 60 * 1000, showObservations); // every 2 mins
  });
}

showRain();
showObservations();
</script>