@import 'maplibre-gl/dist/maplibre-gl.css';

* {
  box-sizing: border-box;
}
html,
body {
  -webkit-text-size-adjust: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
  background-color: #343332;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  user-select: none;
  -webkit-user-select: none;
}
header {
  pointer-events: none;
  position: absolute;
  z-index: 1;
  margin: 0;
  padding: 10px;
  color: #fff;
  background-image: radial-gradient(
    farthest-side at top left,
    #343332,
    transparent
  );
}
header h1 {
  font-size: 20px;
  line-height: 24px;
}
header p {
  font-size: 12px;
  opacity: 0.4;
  margin-top: 3px;
  margin-left: 3px;
}
header > * {
  margin: 0;
  padding: 0;
}
header img {
  vertical-align: middle;
  box-shadow: 0 0 2px rgba(255, 255, 255, 0.25);
  border-radius: 4px;
}

svg {
  fill: currentColor;
}

.ib {
  display: inline-block;
}

#map {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  -webkit-touch-callout: none;
}

#loader {
  pointer-events: none;
  position: absolute;
  right: 48px;
  top: 16px;
  width: 18px;
  height: 18px;
  border: 3px solid;
  border-top-color: rgba(255, 255, 255, 0.75);
  border-right-color: #fff;
  border-bottom-color: transparent;
  border-left-color: transparent;
  border-radius: 123123px;
  animation: 0.3s turning linear infinite;
  transition: 0.3s opacity;
  filter: drop-shadow(0 0 2px #fff);
}
#loader[hidden] {
  display: block;
  opacity: 0;
  animation-play-state: paused;
}
@keyframes turning {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(1turn);
  }
}

#legend {
  color: #fff;
  background-color: rgba(0, 0, 0, 0.2);
  background-image: linear-gradient(to top left, #000, rgba(0, 0, 0, 0.75));
  padding: 1em;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-bottom-left-radius: 18px;
  transition: 0.3s all ease-in-out;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  z-index: 2;
}
#legend[hidden] {
  opacity: 0;
  pointer-events: none;
  bottom: -50px;
}
#legend > div {
  max-width: 400px;
}

#weather-info-labels {
  text-transform: uppercase;
  font-size: 11px;
}
#weather-info-labels span {
  vertical-align: middle;
  display: inline-block;
  padding: 0.5em 0.6em;
  margin: 0 0.5em 0.7em 0;
  background-color: #000;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 123123px;
}
#weather-info-labels span img {
  vertical-align: middle;
  line-height: 0;
}

#heat-stress-indicator {
  animation: heat-flicker 2s ease-in-out infinite alternate;
  transform-origin: center bottom;
}

@keyframes heat-flicker {
  0% {
    opacity: 0.8;
    transform: scale(1) rotate(-8deg);
  }
  25% {
    opacity: 1;
    transform: scale(1.05) rotate(4deg);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.02) rotate(-3deg);
  }
  75% {
    opacity: 1;
    transform: scale(1.08) rotate(6deg);
  }
  100% {
    opacity: 0.85;
    transform: scale(1.03) rotate(-5deg);
  }
}

#wind-compass {
  opacity: 0.7;
  animation: rotate 20s linear both infinite;
}
@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(1turn);
  }
}

#legend .intensity {
  display: flex;
  align-items: center;
}
#legend .intensity h3 {
  white-space: nowrap;
  margin: 0 1em 0 0;
  font-weight: normal;
  font-size: 11px;
  padding: 0;
  text-transform: uppercase;
}
.cloud-mode #legend .intensity {
  display: none;
}

#rain-intensity-gradient {
  padding: 0.5em;
  font-size: 11px;
  display: flex;
  justify-content: space-between;
  width: 100%;
  border-radius: 123123px;
  background: linear-gradient(
    to left,
    #40fffd,
    #3beeec,
    #32d0d2,
    #2cb9bd,
    #229698,
    #1c827d,
    #1b8742,
    #229f44,
    #27b240,
    #2cc53b,
    #30d43e,
    #38ef46,
    #3bfb49,
    #59fa61,
    #fefb63,
    #fdfa53,
    #fdeb50,
    #fdd74a,
    #fcc344,
    #fab03f,
    #faa23d,
    #fb8938,
    #fb7133,
    #f94c2d,
    #f9282a,
    #dd1423,
    #be0f1d,
    #b21867,
    #d028a6,
    #f93df5
  );
}
#rain-intensity-gradient span {
  color: #000;
  text-transform: uppercase;
  background-color: rgba(255, 255, 255, 0.6);
  padding: 0.15em 0.6em;
  border-radius: 123123px;
}

#legend footer {
  margin-top: 2em;
  font-size: 12px;
  opacity: 0.75;
  line-height: 1.5em;
}
#legend footer a {
  color: inherit;
  text-decoration: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
}

#legend-close {
  padding: 10px;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  font-size: 16px;
  border: 0;
  margin-top: 1em;
  width: 100%;
}
#legend-close:active {
  opacity: 0.5;
}

#self-promo {
  margin-top: 2em;
  padding: 0.7em;
  background-color: rgba(30, 30, 30, 0.9);
  border-radius: 5px;
  text-align: center;
}
#self-promo a {
  color: inherit;
  vertical-align: middle;
}
#self-promo img {
  vertical-align: middle;
}

.sns-button {
  display: block;
  margin-top: 0.8em;
  padding: 0.7em;
  background-color: rgba(30, 30, 30, 0.9);
  border-radius: 5px;
  text-align: center;
  text-decoration: none;
  color: #fff;
  line-height: 1;
}
.sns-button svg {
  vertical-align: middle;
}
#telegram-button {
  color: #0088cc;
  border: 2px solid #0088cc;
}

.maplibregl-ctrl-custom-pitch {
  font-weight: bold;
  color: #333;
  font-size: 15px;
  letter-spacing: -0.5px;
}
.active .maplibregl-ctrl-custom-pitch {
  color: #4285f4;
}
.maplibregl-ctrl-custom-pitch span {
  display: block;
  transition: transform 0.3s ease-in-out;
}

.maplibregl-ctrl-custom-clouds-mode {
  color: #333;
}
.maplibregl-ctrl-custom-clouds-mode svg {
  width: 16px;
  height: 16px;
}
.active .maplibregl-ctrl-custom-clouds-mode {
  color: #4285f4;
}
.active .maplibregl-ctrl-custom-clouds-mode svg {
  animation: floating 3s ease-in-out infinite both;
}
@keyframes floating {
  0% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-1px);
  }
  75% {
    transform: translateY(2px);
  }
  100% {
    transform: translateY(0);
  }
}

#player {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 40px;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  width: 100%;
  max-width: 480px;
}
#player-content {
  box-shadow: inset 0 0 0 1px rgba(50, 50, 50, 0.2);
  background-color: rgba(24, 24, 24, 0.9);
  border-radius: 15px;
  padding: 20px;
  margin: 0 10px;
  display: flex;
  align-items: center;
  animation: show-up 1s ease-out both;
}
@supports (backdrop-filter: blur(1px)) or (-webkit-backdrop-filter: blur(1px)) {
  #player-content {
    background-color: rgba(100, 100, 100, 0.4);
    backdrop-filter: blur(30px) saturate(3);
    -webkit-backdrop-filter: blur(30px) saturate(3);
  }
}
@keyframes show-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
#player-content.loading {
  pointer-events: none;
}
#player-range {
  flex-grow: 1;
  position: relative;
}
#player-range svg {
  vertical-align: bottom;
  transition: 0.3s all ease-in-out;
}
#player-range progress {
  appearance: none;
  display: block;
  height: 1px;
  border: 0;
  padding: 0;
  margin: 0;
  width: 100%;
  background-color: #333;
}
.loading #player-range progress {
  background-image: repeating-linear-gradient(
    to right,
    #eee,
    #eee 5px,
    transparent 5px,
    transparent 10px
  );
  animation: move-stripes 5s infinite linear both;
  appearance: none;
}
@keyframes move-stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 100px 0;
  }
}
#player-range progress::-webkit-progress-bar {
  background-color: transparent;
}
#player-range progress::-webkit-progress-value {
  background: transparent linear-gradient(to right, transparent, #fff);
}
#player-range progress::-moz-progress-bar {
  background: transparent linear-gradient(to right, transparent, #fff);
}
#player-time {
  position: absolute;
  left: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.75);
  font-variant-numeric: tabular-nums;
  white-space: nowrap;
  text-align: right;
  transition: 0.3s ease-in-out;
  transition-property: text-shadow, color;
  will-change: left;
}
#player-time.now {
  color: white;
  text-shadow: 0 0 10px #fff, 0 0 10px #fff;
}
#player-slider {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
}
#player-slider input {
  width: 100%;
  margin: 5px 0 0;
}
#player-labels {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  line-height: 11px;
  pointer-events: none;
  color: slategray;
  font-variant-numeric: tabular-nums;
  width: 100%;
  margin-top: 9px;
}
#player-labels > span {
  padding: 0 1px;
}
#player-button {
  color: slategray;
  margin-right: 15px;
  cursor: pointer;
  text-align: center;
}
#player-button.playing {
  color: #fff;
}
.loading #player-button {
  opacity: 0.6;
  pointer-events: none;
}
#player-icon {
  display: inline-block;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 10px;
  line-height: 0;
}
#player-button:hover #player-icon {
  border-color: rgba(255, 255, 255, 0.8);
}
.playing #player-icon {
  animation: glow-shadow 1s infinite alternate ease-in-out;
}
@keyframes glow-shadow {
  0% {
    box-shadow: 0 0 7.5px rgba(255, 255, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(255, 255, 255, 1);
  }
}
#player-icon svg {
  transition: 0.3s all ease-in-out;
}
#player-footer {
  margin-top: 1em;
  font-size: 11px;
  color: slategray;
}
#player-footer b {
  color: whitesmoke;
}

#info-button {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 5px 10px;
  border: 0;
  margin: 0;
  background-color: transparent;
  cursor: pointer;
  outline: 0;
}
#info-button:active {
  opacity: 0.5;
}
#info-button svg {
  fill: rgba(255, 255, 255, 0.95);
}

input[type='range'] {
  -webkit-appearance: none; /* Hides the slider so that custom slider can be made */
  width: calc(100% + 16px); /* Specific width is required for Firefox. */
  background: transparent; /* Otherwise white in Chrome */
  height: 100%;
  position: absolute;
  top: 0px;
  bottom: 0;
  right: 0;
  left: -8px;
  padding: 0;
  margin: 0;
  border: 0;
  -webkit-tap-highlight-color: transparent;
  outline: none;
}
input::-moz-focus-inner {
  border: 0;
}
input[type='range']:focus {
  outline: none;
}

.loading input[type='range'] {
  pointer-events: none;
}

input[type='range']::-webkit-slider-runnable-track {
  width: 100%;
  height: 100%;
  cursor: pointer;
  background-color: transparent;
  display: flex;
  align-items: flex-end;
}
input[type='range']::-moz-range-track {
  width: 100%;
  height: 100%;
  cursor: pointer;
  background-color: transparent;
  display: flex;
  align-items: flex-end;
}

input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 20px;
  background-color: lightgray;
  box-shadow: 0 2px 10px #000;
  cursor: pointer;
  margin-top: 40px;
  transform: translateY(-50%);
}
input[type='range']::-moz-range-thumb {
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border: 0;
  border-radius: 20px;
  background-color: lightgray;
  box-shadow: 0 2px 10px #000;
  cursor: pointer;
  transform: translateY(5px); /* weird hack, yeah */
}

input[type='range']::-webkit-slider-thumb:hover {
  background-color: #fff;
}
input[type='range']::-moz-range-thumb:hover {
  background-color: #fff;
}

input[type='range']::-webkit-slider-thumb:active {
  background-color: #fff;
  box-shadow: 0 2px 10px #fff;
}
input[type='range']::-moz-range-thumb:active {
  background-color: #fff;
  box-shadow: 0 2px 10px #fff;
}

#player-sparkline {
  height: 40px;
  width: 100%;
  background-image: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0.03)
  );
  transition: all 1s ease-in-out;
}
.loading #player-sparkline {
  opacity: 0.5;
}

.maplibregl-ctrl {
  filter: invert(.75);
}

/* Weather popover styles */
#weather-popover {
  border: none;
  padding: 0;
  color: #fff;
  background: transparent;
  width: 100%;
  max-width: 100vw;
  max-height: 75vh;
  overflow: hidden;
  margin: 0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  top: auto;
  transform: translateY(100%);
}

#weather-popover:popover-open {
  animation: slide-up 0.15s ease-out forwards .15s;
}

#weather-popover.closing {
  animation: slide-down 0.25s ease-out forwards;
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-down {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

#weather-popover-content {
  background-color: rgba(24, 24, 24, 0.95);
  border-radius: 15px 15px 0 0;
  padding: 20px;
  color: #fff;
  position: relative;
  max-height: 60vh;
  overflow-y: auto;
  cursor: pointer;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

#weather-popover-body h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

@media (min-width: 768px) {
  #weather-popover {
    max-width: 500px;
    left: 50%;
    right: auto;
    transform: translateX(-50%) translateY(100%);
  }

  #weather-popover:popover-open {
    animation: slide-up 0.3s ease-out forwards;
  }

  #weather-popover.closing {
    animation: slide-down 0.3s ease-out forwards;
  }
}

.weather-station {
  margin-bottom: 15px;
}

.weather-station:last-child {
  margin-bottom: 0;
}

.station-name {
  font-weight: normal;
  font-size: 11px;
  margin-bottom: 10px;
  opacity: 0.7;
}

.weather-readings {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 6px;
}

.weather-reading {
  display: flex;
  flex-direction: column;
  font-size: 13px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  text-align: center;
}

.weather-reading-header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  opacity: 0.7;
  margin-bottom: 4px;
}

.weather-reading-header .emoji {
  margin-right: 4px;
  font-size: 12px;
  flex-shrink: 0;
}

.weather-reading-header span:last-child {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.weather-reading-value {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
}

.heat-risk {
  grid-column: 1 / -1;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}

.heat-risk .weather-reading-header {
  margin-bottom: 0;
  justify-content: flex-start;
}

.heat-risk.high {
  color: #ff6b6b;
}

.heat-risk.moderate {
  color: #ffa726;
}

.wind-arrow {
  width: 12px;
  height: 12px;
  margin-right: 4px;
  flex-shrink: 0;
}

/* Tap circle animation */
#tap-circle {
  display: none;
  position: absolute;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  animation: tap-pulse 0.6s ease-out;
}

#tap-circle.long-duration {
  box-shadow: 0 0 16px white;
  animation-duration: 1.5s;
}

@keyframes tap-pulse {
  0% {
    transform: scale(0.5);
    opacity: 0.8;
  }
  33% {
    transform: scale(1.1);
    opacity: 0.6;
  }
  66% {
    transform: scale(1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.screensaver {
  .maplibregl-ctrl-top-right, #player-button, #info-button {
    display: none;
  }
  #loader {
    right: 16px;
  }
  #player {
    bottom: 0;
  }
  #player-content {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}