<!DOCTYPE html>
<html lang="en" style="background: #343332">
  <head>
    <meta charset="utf-8" />
    <title>Check Weather SG ☀️🌧 Yet another weather app for Singapore</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, viewport-fit=cover"
    />
    <meta name="description" content="Yet another weather app for Singapore" />
    <link rel="preconnect" href="https://api.maptiler.com" crossorigin />
    <link
      rel="preconnect"
      href="https://firestore.googleapis.com"
      crossorigin
    />
    <link
      rel="preload"
      as="fetch"
      href="https://api2.checkweather.sg/v1/observations"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="./app.css" />
    <link
      rel="icon"
      type="image/png"
      sizes="192x192"
      href="./icons/icon-192.png"
    />
    <link rel="apple-touch-icon" sizes="192x192" href="./icons/icon-192.png" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="Check Weather SG" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="theme-color" content="#343332" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@checkweathersg" />
    <meta
      name="twitter:url"
      property="og:url"
      content="https://checkweather.sg"
    />
    <meta name="twitter:title" property="og:title" content="Check Weather SG" />
    <meta
      name="twitter:description"
      property="og:description"
      content="Yet another weather app for Singapore"
    />
    <meta
      name="twitter:image"
      property="og:image"
      content="https://rainshot.checkweather.sg/"
    />
    <script
      defer
      data-domain="checkweather.sg"
      src="https://plausible.io/js/plausible.js"
    ></script>
  </head>
  <header>
    <h1>
      <img
        src="icons/icon-32.png"
        loading="lazy"
        width="24"
        height="24"
        alt=""
      />
      Check Weather SG
    </h1>
    <p>Yet another weather app for Singapore</p>
  </header>
  <div id="map"></div>
  <div id="loader" hidden></div>
  <button id="info-button">
    <svg height="24" viewBox="0 0 24 24" width="24">
      <path
        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"
      />
    </svg>
  </button>
  <div id="legend" hidden>
    <div>
      <h2>Legend</h2>
      <div id="weather-info-labels">
        <span class="text-label" style="color: yellow">Temperature (°C)</span>
        <span class="text-label" style="color: orange"
          >Relative Humidity (%)</span
        >
        <span class="text-label" style="color: aqua">Rainfall (mm)</span>
        <span class="icon-label"
          ><img
            src="assets/arrow-down.svg"
            loading="lazy"
            width="12"
            height="12"
            id="wind-compass"
            alt="Wind direction arrow"
          />
          Wind Direction</span
        >
        <span class="icon-label"
          ><img
            src="assets/fire-outline-black.png"
            loading="lazy"
            width="10"
            height="12"
            id="heat-stress-indicator"
            alt="Heat stress indicator"
          />
          Heat Stress</span
        >
      </div>
      <div class="intensity">
        <h3>Rain intensity</h3>
        <div id="rain-intensity-gradient">
          <span>Heavy</span>
          <span>Moderate</span>
          <span>Light</span>
        </div>
      </div>
      <div id="self-promo">
        Like my work?
        <a target="_blank" href="https://www.buymeacoffee.com/cheeaun">
          Buy me a coffee!
        </a>
      </div>
      <!-- <a
        href="https://twitter.com/checkweathersg"
        target="_blank"
        id="twitter-button"
        class="sns-button"
      >
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path
            d="M23.95 4.57a10 10 0 01-2.82.77 4.96 4.96 0 002.16-2.72c-.95.56-2 .96-3.12 1.19a4.92 4.92 0 00-8.38 4.48A13.93 13.93 0 011.63 3.16a4.92 4.92 0 001.52 6.57 4.9 4.9 0 01-2.23-.61v.06c0 2.38 1.7 4.37 3.95 4.82a5 5 0 01-2.21.09 4.94 4.94 0 004.6 3.42A9.87 9.87 0 010 19.54a14 14 0 007.56 2.21c9.05 0 14-7.5 14-13.98 0-.21 0-.42-.02-.63A9.94 9.94 0 0024 4.59l-.05-.02z"
          />
        </svg>
        Follow @checkweathersg
      </a> -->
      <a
        href="https://t.me/checkweathersg"
        target="_blank"
        id="telegram-button"
        class="sns-button"
      >
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path d="M11.94 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.06 0zm4.97 7.22c.1 0 .32.03.46.14a.5.5 0 0 1 .17.33c.02.1.04.3.02.47-.18 1.9-.96 6.5-1.36 8.63-.17.9-.5 1.2-.82 1.23-.7.06-1.22-.46-1.9-.9-1.05-.7-1.65-1.13-2.68-1.8-1.18-.78-.41-1.21.26-1.91.18-.19 3.25-2.98 3.3-3.23.02-.04.02-.15-.05-.22s-.17-.04-.25-.02c-.1.02-1.79 1.14-5.06 3.35-.48.33-.91.49-1.3.48a8.5 8.5 0 0 1-1.86-.44c-.76-.25-1.35-.38-1.3-.8.03-.21.32-.43.9-.66a556.4 556.4 0 0 1 6.99-3.01c3.33-1.39 4.03-1.63 4.48-1.64z"/>
        </svg>
        Subscribe to @checkweathersg
      </a>
      <a
        href="https://apps.apple.com/app/check-weather-sg/id1510170224"
        target="_blank"
        id="ios-button"
        class="sns-button"
        > Download on the App Store</a
      >
      <a
        href="https://mastodon.social/@<EMAIL>"
        target="_blank"
        id="mastodon-button"
        class="sns-button"
      >
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path d="M23.27 5.31c-.35-2.57-2.62-4.6-5.3-5C17.5.24 15.78 0 11.8 0h-.03C7.8 0 6.95.24 6.5.3 3.88.7 1.5 2.53.92 5.14a15.7 15.7 0 0 0-.26 4c.07 1.88.09 3.75.26 5.61.12 1.24.33 2.47.62 3.68.55 2.24 2.78 4.1 4.96 4.86a13.41 13.41 0 0 0 8.04.17c.59-.19 1.27-.4 1.78-.76a.06.06 0 0 0 .02-.04v-1.8a.05.05 0 0 0-.02-.05.05.05 0 0 0-.05 0 20.28 20.28 0 0 1-4.7.54c-2.73 0-3.47-1.29-3.68-1.82a5.6 5.6 0 0 1-.32-1.43.05.05 0 0 1 .07-.06c1.52.36 3.07.55 4.63.55l1.13-.01c1.57-.05 3.22-.13 4.76-.42l.11-.03c2.44-.46 4.76-1.92 5-5.6l.02-1.67c0-.51.17-3.63-.02-5.55zm-3.75 9.2h-2.56V8.29c0-1.3-.55-1.98-1.67-1.98-1.23 0-1.85.8-1.85 2.35v3.4H10.9v-3.4c0-1.56-.62-2.35-1.85-2.35-1.11 0-1.67.67-1.67 1.98v6.22H4.82V8.1a4.6 4.6 0 0 1 1.01-3.13 3.53 3.53 0 0 1 2.74-1.16c1.31 0 2.3.5 2.97 1.5l.63 1.06.64-1.06c.66-1 1.65-1.5 2.96-1.5 1.13 0 2.04.4 2.74 1.16a4.57 4.57 0 0 1 1.01 3.12z"/>
        </svg>
        Follow @<EMAIL>
      </a>
      <footer>
        <span class="ib"
          >Built by
          <a
            href="https://twitter.com/cheeaun"
            target="_blank"
            rel="noopener nofollow"
            >@cheeaun</a
          >.</span
        >
        <span class="ib">
          Open-sourced on
          <a
            href="https://github.com/cheeaun/checkweather-sg"
            target="_blank"
            rel="noopener nofollow"
            >GitHub</a
          >.
        </span>
        <br />
        <span class="ib">
          &copy;
          <a
            href="https://www.maptiler.com/copyright/"
            target="_blank"
            rel="noopener nofollow"
          >
            MapTiler
          </a>
        </span>
        <span class="ib">
          &copy;
          <a
            href="https://www.openstreetmap.org/copyright"
            target="_blank"
            rel="noopener nofollow"
          >
            OpenStreetMap contributors
          </a>
        </span>
        <span class="ib">
          &copy;
          <a
            href="https://data.gov.sg/privacy-and-website-terms#site-terms"
            target="_blank"
            rel="noopener nofollow"
          >
            Data.gov.sg
          </a>
        </span>
        <span class="ib">
          &copy;
          <a
            href="http://www.weather.gov.sg/terms-of-use"
            target="_blank"
            rel="noopener nofollow"
          >
            Meteorological Service Singapore
          </a>
        </span>
        <span class="ib">
          &copy;
          <a
            href="http://www.nea.gov.sg/open-data-licence/"
            target="_blank"
            rel="noopener nofollow"
          >
            National Environment Agency
          </a>
        </span>
        <!-- <span class="ib"
          >&copy;
          <a
            href="https://www.mapbox.com/about/maps/"
            target="_blank"
            rel="noopener nofollow"
            >Mapbox</a
          ></span
        >
        <span class="ib"
          >&copy;
          <a
            href="http://www.openstreetmap.org/about/"
            target="_blank"
            rel="noopener nofollow"
            >OpenStreetMap</a
          ></span
        >
        <span class="ib"
          >&copy;
          <a
            href="https://data.gov.sg/privacy-and-website-terms#site-terms"
            target="_blank"
            rel="noopener nofollow"
            >Data.gov.sg</a
          ></span
        >
        <span class="ib"
          >&copy;
          <a
            href="http://www.weather.gov.sg/terms-of-use"
            target="_blank"
            rel="noopener nofollow"
            >Meteorological Service Singapore</a
          ></span
        >
        <span class="ib"
          >&copy;
          <a
            href="http://www.nea.gov.sg/open-data-licence/"
            target="_blank"
            rel="noopener nofollow"
            >National Environment Agency</a
          ></span
        > -->
      </footer>
      <button id="legend-close">Close</button>
    </div>
  </div>
  <div id="player"></div>

  <!-- Tap circle indicator -->
  <div id="tap-circle"></div>

  <!-- Weather stations popover -->
  <div id="weather-popover" popover="manual">
    <div id="weather-popover-content">
      <div id="weather-popover-body"></div>
    </div>
  </div>

  <svg width="0" height="0">
    <defs>
      <linearGradient id="spark-gradient" x1="0%" y1="100%" x2="0%" y2="0%">
        <stop offset="0%" style="stop-color: rgba(255, 255, 255, 0.03)" />
        <stop offset="100%" style="stop-color: rgba(255, 255, 255, 0.3)" />
      </linearGradient>
    </defs>
  </svg>
  <script type="module" src="./app.jsx"></script>
</html>
